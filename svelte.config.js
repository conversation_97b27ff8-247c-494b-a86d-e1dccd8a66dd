import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://svelte.dev/docs/kit/integrations
	// for more information about preprocessors
	preprocess: vitePreprocess(),
	// Temporarily disable runes mode due to lucide-svelte compatibility
	// compilerOptions: {
	// 	runes: true
	// },
	kit: {
		adapter: adapter({
			// Enable fallback for SPA mode to handle dynamic routes
			fallback: 'index.html',
			// Don't prerender dynamic auth routes
			strict: false
		})
	}
};

export default config;
