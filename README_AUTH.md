# SvelteKit 5 + Zitadel Authentication

A complete authentication solution built with SvelteKit 5, Auth.js, and Zitadel OIDC integration, featuring shadcn-svelte components and TypeScript.

## 🔐 Authentication Features

### Core Features

- **Zitadel OIDC Integration** - Enterprise-grade authentication
- **Auth.js/NextAuth** - Industry-standard authentication library
- **Route Protection** - Server-side authentication guards
- **Session Management** - Persistent sessions across page refreshes
- **TypeScript Types** - Full type safety for authentication

### UI Components

- **Authentication UI** - Pre-built login, profile, and navigation components
- **Error Handling** - Comprehensive error boundaries and user feedback
- **Responsive Design** - Mobile-first responsive layouts
- **Loading States** - Smooth loading indicators

## 🚀 Quick Start

### 1. Environment Setup

Copy the environment template:

```bash
cp .env.example .env
```

Update `.env` with your Zitadel configuration:

```env
# Auth.js Configuration
AUTH_SECRET=your-auth-secret-here-generate-with-openssl-rand-base64-32

# Zitadel Configuration
ZITADEL_ISSUER=https://zitadel.local.acjzz.xyz
ZITADEL_CLIENT_ID=your-zitadel-client-id
ZITADEL_CLIENT_SECRET=your-zitadel-client-secret

# Application Configuration
PUBLIC_APP_URL=http://localhost:5173
```

### 2. Configure Zitadel

Follow the [Zitadel Setup Guide](./docs/ZITADEL_SETUP.md) to:

1. Create a new application in Zitadel
2. Configure redirect URIs
3. Set up scopes and permissions
4. Get client credentials

### 3. Start Development

```bash
npm run dev
```

Navigate to `http://localhost:5173` and test the authentication!

## 📁 Project Structure

```
src/
├── auth.ts                 # Auth.js configuration
├── hooks.server.ts         # SvelteKit server hooks
├── lib/
│   ├── auth/               # Authentication utilities
│   │   ├── guards.ts       # Route protection
│   │   └── index.ts        # Auth exports
│   ├── components/auth/    # Authentication UI components
│   ├── stores/auth.ts      # Authentication stores
│   ├── types/auth.ts       # TypeScript types
│   └── utils/auth-errors.ts # Error handling
├── routes/
│   ├── auth/               # Authentication routes
│   ├── dashboard/          # Protected dashboard
│   ├── profile/            # User profile
│   ├── protected/          # Example protected route
│   └── settings/           # User settings
```

## 🛡️ Protected Routes

The application includes several example protected routes:

- **`/dashboard`** - User dashboard with profile information
- **`/profile`** - User profile management
- **`/settings`** - Account settings and preferences
- **`/protected`** - Example secure content area

All protected routes automatically redirect unauthenticated users to the sign-in page.

## 🎯 UI Components

### Authentication Components

- **`<AuthButton />`** - Smart sign-in/sign-out button
- **`<UserProfile />`** - User avatar dropdown with menu
- **`<AuthStatus />`** - Authentication status indicator
- **`<Navbar />`** - Navigation with authentication state
- **`<ErrorBoundary />`** - Comprehensive error handling
- **`<LoadingSpinner />`** - Loading state management

### Usage Example

```svelte
<script lang="ts">
	import { page } from '$app/stores';
	import AuthButton from '$lib/components/auth/AuthButton.svelte';
	import UserProfile from '$lib/components/auth/UserProfile.svelte';

	$: session = $page.data.session;
</script>

{#if session?.user}
	<UserProfile />
{:else}
	<AuthButton />
{/if}
```

## 🔒 Route Protection

### Server-Side Protection

```typescript
// src/routes/protected/+page.server.ts
import { requireAuth } from '$lib/auth/guards';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
	const session = await requireAuth(event);
	return {
		user: session.user
	};
};
```

### Role-Based Protection

```typescript
// Require specific roles
const session = await requireRole(event, ['admin', 'user']);
```

## 📚 Documentation

- **[Authentication Guide](./docs/AUTHENTICATION_GUIDE.md)** - Complete authentication documentation
- **[Zitadel Setup](./docs/ZITADEL_SETUP.md)** - Zitadel configuration guide
- **[API Reference](./docs/AUTHENTICATION_GUIDE.md#api-reference)** - Authentication API documentation

## 🚀 Deployment

### Environment Variables

Set these in production:

- `AUTH_SECRET` - Secure secret for JWT signing
- `ZITADEL_ISSUER` - Your Zitadel instance URL
- `ZITADEL_CLIENT_ID` - Zitadel application client ID
- `ZITADEL_CLIENT_SECRET` - Zitadel application client secret
- `PUBLIC_APP_URL` - Your application's public URL

### Docker Deployment

```bash
# Build and run
docker build -f Dockerfile.prod -t sveltekit-auth .
docker run -p 3000:3000 --env-file .env sveltekit-auth
```

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run test` - Run all tests
- `npm run lint` - Lint and format code
- `npm run check` - Type check

### Testing Authentication

1. Start the development server
2. Navigate to the home page
3. Click "Sign In" to test the authentication flow
4. Try accessing protected routes like `/dashboard`
5. Test sign-out functionality

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `npm run ci`
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

---

For detailed setup instructions and troubleshooting, see the [Authentication Guide](./docs/AUTHENTICATION_GUIDE.md).
