services:
  svelte-app:
    build:
      context: .
      dockerfile: Dockerfile.prod
      args:
        - AUTH_SECRET=${AUTH_SECRET}
        - ZITADEL_ISSUER=${ZITADEL_ISSUER}
        - ZITADEL_CLIENT_ID=${ZITADEL_CLIENT_ID}
        - ZITADEL_CLIENT_SECRET=${ZITADEL_CLIENT_SECRET}
        - PUBLIC_APP_URL=${PUBLIC_APP_URL}
    ports:
      - '8080:80'
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:80/']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - svelte-prod-network
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

networks:
  svelte-prod-network:
    driver: bridge
