# Zitadel Configuration for SvelteKit Authentication

This guide walks you through setting up Zitadel for authentication with your SvelteKit 5 application.

## Prerequisites

- Access to your Zitadel instance at `https://zitadel.local.acjzz.xyz`
- Admin privileges to create applications in Zitadel

## Step 1: Create a New Application

1. **Login to Zitadel Console**
   - Navigate to `https://zitadel.local.acjzz.xyz`
   - Login with your admin credentials

2. **Create New Project (if needed)**
   - Go to "Projects" in the main navigation
   - Click "Create New Project" if you don't have one
   - Give it a meaningful name like "SvelteKit App"

3. **Create New Application**
   - Within your project, click "Applications"
   - Click "Create New Application"
   - Choose "Web Application"
   - Set application name: "SvelteKit Auth App"

## Step 2: Configure Application Settings

### Basic Configuration

- **Application Type**: Web Application
- **Authentication Method**: PKCE (recommended) or Basic
- **Grant Types**:
  - Authorization Code
  - Refresh Token

### Redirect URIs

Add the following redirect URIs:

**Development:**

```
http://localhost:5173/auth/callback/zitadel
```

**Production:**

```
https://your-domain.com/auth/callback/zitadel
```

### Post Logout Redirect URIs

Add these for proper logout handling:

**Development:**

```
http://localhost:5173
```

**Production:**

```
https://your-domain.com
```

## Step 3: Configure Scopes and Claims

### Required Scopes

Ensure these scopes are enabled:

- `openid` (required for OIDC)
- `profile` (for user profile information)
- `email` (for user email)

### Optional Scopes

You may also want to enable:

- `offline_access` (for refresh tokens)
- `urn:zitadel:iam:org:project:id:zitadel:aud` (for Zitadel-specific claims)

## Step 4: Get Client Credentials

After creating the application:

1. **Client ID**: Copy the generated Client ID
2. **Client Secret**:
   - If using Basic authentication, copy the Client Secret
   - If using PKCE, no client secret is needed

## Step 5: Update Environment Variables

Update your `.env` file with the Zitadel configuration:

```env
# Zitadel Configuration
ZITADEL_ISSUER=https://zitadel.local.acjzz.xyz
ZITADEL_CLIENT_ID=your-actual-client-id-here
ZITADEL_CLIENT_SECRET=your-actual-client-secret-here
```

## Step 6: Enable Development Mode (Optional)

For local development, you may need to:

1. Go to your application settings in Zitadel
2. Enable "Development Mode" if available
3. This allows HTTP redirects for localhost

## Verification

To verify your configuration:

1. Start your SvelteKit application: `npm run dev`
2. Navigate to `http://localhost:5173/auth/signin`
3. You should be redirected to Zitadel for authentication
4. After successful login, you should be redirected back to your app

## Troubleshooting

### Common Issues

1. **Invalid Redirect URI**
   - Ensure redirect URIs in Zitadel exactly match your application URLs
   - Check for trailing slashes and protocol (http vs https)

2. **CORS Issues**
   - Ensure your domain is properly configured in Zitadel
   - Check that CORS settings allow your application domain

3. **Scope Issues**
   - Verify that required scopes (openid, profile, email) are enabled
   - Check that your application has permission to access these scopes

4. **Client Secret Issues**
   - Ensure client secret is correctly copied (no extra spaces)
   - Verify that the authentication method matches your configuration

## Security Considerations

1. **Client Secret**: Keep your client secret secure and never expose it in client-side code
2. **HTTPS**: Always use HTTPS in production
3. **Redirect URIs**: Only add trusted redirect URIs
4. **Scopes**: Only request the minimum scopes needed for your application

## Testing the Setup

After completing the configuration:

1. **Start your application**:

   ```bash
   npm run dev
   ```

2. **Test the authentication flow**:
   - Navigate to `http://localhost:5173`
   - Click "Sign In" button
   - You should be redirected to Zitadel
   - Complete authentication
   - You should be redirected back to your app

3. **Verify protected routes**:
   - Try accessing `/dashboard` without authentication
   - You should be redirected to sign-in
   - After signing in, you should access the dashboard

## Next Steps

After completing this setup:

1. Test the complete authentication flow
2. Explore the protected routes (`/dashboard`, `/profile`, `/protected`)
3. Customize the UI components to match your brand
4. Add role-based access control if needed
5. Configure additional Zitadel features (MFA, etc.)

## Integration with Existing Applications

To integrate this authentication system into an existing SvelteKit application:

1. **Install dependencies**:

   ```bash
   npm install @auth/sveltekit @auth/core
   ```

2. **Copy authentication files**:
   - `src/auth.ts`
   - `src/hooks.server.ts`
   - `src/lib/auth/`
   - `src/lib/components/auth/`
   - `src/lib/stores/auth.ts`
   - `src/lib/types/auth.ts`

3. **Update your layout**:
   - Add `AuthProvider` and `ErrorBoundary` components
   - Include authentication UI components

4. **Protect your routes**:
   - Use authentication guards in your `+page.server.ts` files
   - Add authentication checks to your components
