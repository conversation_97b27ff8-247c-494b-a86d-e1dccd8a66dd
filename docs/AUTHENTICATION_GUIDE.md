# SvelteKit 5 + Zitadel Authentication Guide

This guide provides comprehensive documentation for the authentication system implemented in this SvelteKit 5 application using Auth.js and Zitadel.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Setup Instructions](#setup-instructions)
4. [Usage Examples](#usage-examples)
5. [API Reference](#api-reference)
6. [Troubleshooting](#troubleshooting)
7. [Deployment](#deployment)

## Overview

This application implements a complete authentication solution using:

- **SvelteKit 5**: Modern full-stack framework
- **Auth.js**: Industry-standard authentication library
- **Zitadel**: Enterprise-grade identity and access management
- **TypeScript**: Full type safety
- **shadcn-svelte**: Beautiful UI components

### Key Features

- ✅ OIDC/OAuth 2.0 authentication with Zitadel
- ✅ Server-side session management
- ✅ Route protection and guards
- ✅ TypeScript type safety
- ✅ Error handling and user feedback
- ✅ Responsive UI components
- ✅ Session persistence across page refreshes

## Architecture

### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant SvelteKit
    participant AuthJS
    participant Zitadel

    User->>SvelteKit: Access protected route
    SvelteKit->>AuthJS: Check session
    AuthJS->>SvelteKit: No valid session
    SvelteKit->>User: Redirect to sign-in
    User->>Zitadel: Authenticate
    Zitadel->>AuthJS: Return tokens
    AuthJS->>SvelteKit: Create session
    SvelteKit->>User: Access granted
```

### File Structure

```
src/
├── auth.ts                 # Auth.js configuration
├── hooks.server.ts         # SvelteKit server hooks
├── lib/
│   ├── auth/
│   │   ├── guards.ts       # Route protection utilities
│   │   └── index.ts        # Auth exports
│   ├── components/auth/    # Authentication UI components
│   ├── stores/auth.ts      # Authentication stores
│   ├── types/auth.ts       # TypeScript types
│   └── utils/auth-errors.ts # Error handling
└── routes/
    ├── auth/               # Authentication routes
    ├── dashboard/          # Protected dashboard
    ├── profile/            # User profile
    └── protected/          # Example protected route
```

## Setup Instructions

### 1. Environment Configuration

Create a `.env` file with the following variables:

```env
# Auth.js Configuration
AUTH_SECRET=your-auth-secret-here-generate-with-openssl-rand-base64-32

# Zitadel Configuration
ZITADEL_ISSUER=https://zitadel.local.acjzz.xyz
ZITADEL_CLIENT_ID=your-zitadel-client-id
ZITADEL_CLIENT_SECRET=your-zitadel-client-secret

# Application Configuration
PUBLIC_APP_URL=http://localhost:5173
```

### 2. Zitadel Setup

Follow the [Zitadel Setup Guide](./ZITADEL_SETUP.md) for detailed configuration instructions.

### 3. Install Dependencies

```bash
npm install @auth/sveltekit @auth/core
```

### 4. Start Development Server

```bash
npm run dev
```

## Usage Examples

### Protecting Routes

```typescript
// src/routes/protected/+page.server.ts
import { requireAuth } from '$lib/auth/guards';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
	const session = await requireAuth(event);
	return {
		user: session.user
	};
};
```

### Using Authentication in Components

```svelte
<script lang="ts">
	import { page } from '$app/stores';
	import AuthButton from '$lib/components/auth/AuthButton.svelte';

	$: session = $page.data.session;
	$: user = session?.user;
</script>

{#if user}
	<p>Welcome, {user.name}!</p>
{:else}
	<AuthButton />
{/if}
```

### Using Authentication Stores

```typescript
import { session, user, isAuthenticated } from '$lib/stores/auth';

// Reactive authentication state
$: if ($isAuthenticated) {
	console.log('User is authenticated:', $user);
}
```

## API Reference

### Authentication Guards

#### `requireAuth(event, redirectTo?)`

Protects a route by requiring authentication.

```typescript
const session = await requireAuth(event);
```

#### `requireRole(event, roles, redirectTo?)`

Protects a route by requiring specific roles.

```typescript
const session = await requireRole(event, ['admin', 'user']);
```

#### `optionalAuth(event)`

Provides session information without requiring authentication.

```typescript
const session = await optionalAuth(event);
```

### Authentication Stores

#### `session`

Reactive store containing the current session.

#### `user`

Reactive store containing the current user.

#### `isAuthenticated`

Reactive store indicating authentication status.

#### `authLoading`

Reactive store for loading state.

#### `authError`

Reactive store for error state.

### UI Components

#### `<AuthButton />`

Sign in/out button with automatic state management.

#### `<UserProfile />`

User profile dropdown with avatar and menu.

#### `<AuthStatus />`

Authentication status indicator.

#### `<ErrorBoundary />`

Error handling wrapper component.

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI" error**
   - Ensure redirect URIs in Zitadel match your application URLs exactly
   - Check for trailing slashes and protocol (http vs https)

2. **Session not persisting**
   - Verify `AUTH_SECRET` is set and consistent
   - Check that cookies are enabled in the browser

3. **CORS errors**
   - Ensure your domain is properly configured in Zitadel
   - Check CORS settings in your deployment environment

4. **TypeScript errors**
   - Run `npm run check` to verify TypeScript configuration
   - Ensure all Auth.js types are properly imported

### Debug Mode

Enable debug logging by setting:

```env
AUTH_DEBUG=true
```

## Deployment

### Environment Variables

Ensure all environment variables are set in your production environment:

- `AUTH_SECRET`: Generate a secure secret for production
- `ZITADEL_ISSUER`: Your production Zitadel instance URL
- `ZITADEL_CLIENT_ID`: Production client ID
- `ZITADEL_CLIENT_SECRET`: Production client secret
- `PUBLIC_APP_URL`: Your production application URL

### Zitadel Configuration

Update your Zitadel application settings for production:

1. Add production redirect URIs
2. Update CORS settings
3. Configure proper scopes and permissions

### Security Considerations

1. **HTTPS**: Always use HTTPS in production
2. **Secrets**: Keep client secrets secure and rotate regularly
3. **Scopes**: Only request minimum required scopes
4. **Sessions**: Configure appropriate session timeouts

### Docker Deployment

The application includes Docker configuration for easy deployment:

```bash
# Build production image
docker build -f Dockerfile.prod -t sveltekit-auth .

# Run container
docker run -p 3000:3000 --env-file .env sveltekit-auth
```

## Support

For additional help:

1. Check the [Zitadel Documentation](https://zitadel.com/docs)
2. Review [Auth.js Documentation](https://authjs.dev)
3. See [SvelteKit Documentation](https://kit.svelte.dev)
4. Open an issue in the project repository
