<script lang="ts">
	import '../app.css';
	import PerformanceMonitor from '$lib/components/dev/PerformanceMonitor.svelte';
	import Navbar from '$lib/components/auth/Navbar.svelte';
	import AuthProvider from '$lib/components/auth/AuthProvider.svelte';
	import ErrorBoundary from '$lib/components/auth/ErrorBoundary.svelte';
	import LoadingSpinner from '$lib/components/auth/LoadingSpinner.svelte';

	const { children } = $props();
</script>

<AuthProvider>
	<ErrorBoundary>
		<div class="bg-background min-h-screen">
			<Navbar />
			<main>
				{@render children()}
			</main>
		</div>
		<LoadingSpinner overlay={true} />
	</ErrorBoundary>
</AuthProvider>

<!-- Performance monitoring in development -->
<PerformanceMonitor />
