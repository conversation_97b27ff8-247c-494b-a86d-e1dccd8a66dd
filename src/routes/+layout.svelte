<script lang="ts">
	import { onMount } from 'svelte';
	import '../app.css';
	import PerformanceMonitor from '$lib/components/dev/PerformanceMonitor.svelte';
	import Navbar from '$lib/components/auth/Navbar.svelte';
	import AuthProvider from '$lib/components/auth/AuthProvider.svelte';
	import ErrorBoundary from '$lib/components/auth/ErrorBoundary.svelte';
	import LoadingSpinner from '$lib/components/auth/LoadingSpinner.svelte';
	import { initAuth } from '$lib/stores/auth';

	const { children } = $props();

	// Initialize authentication when the app starts
	onMount(() => {
		initAuth();
	});
</script>

<AuthProvider>
	<ErrorBoundary>
		<div class="bg-background min-h-screen">
			<Navbar />
			<main>
				{@render children()}
			</main>
		</div>
		<LoadingSpinner overlay={true} />
	</ErrorBoundary>
</AuthProvider>

<!-- Performance monitoring in development -->
<PerformanceMonitor />
