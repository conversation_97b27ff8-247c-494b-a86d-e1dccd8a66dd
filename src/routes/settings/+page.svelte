<script lang="ts">
	import { page } from '$app/stores';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card/index.js';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Switch } from '$lib/components/ui/switch/index.js';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import AuthButton from '$lib/components/auth/AuthButton.svelte';
	import { Settings, Shield, Bell, Eye, ArrowLeft, ExternalLink } from 'lucide-svelte';

	$: data = $page.data;
	$: user = data.user;
</script>

<svelte:head>
	<title>Settings - SvelteKit Auth</title>
</svelte:head>

<div class="container mx-auto space-y-8 p-8">
	<header class="space-y-4">
		<div class="flex items-center gap-4">
			<Button href="/dashboard" variant="outline" size="sm">
				<ArrowLeft class="mr-2 h-4 w-4" />
				Back to Dashboard
			</Button>
		</div>
		<div class="flex items-center gap-3">
			<div
				class="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20"
			>
				<Settings class="h-6 w-6 text-blue-600 dark:text-blue-400" />
			</div>
			<div>
				<h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">Account Settings</h1>
				<p class="text-gray-600 dark:text-gray-400">
					Manage your account preferences and security settings
				</p>
			</div>
		</div>
	</header>

	<div class="grid gap-6 lg:grid-cols-3">
		<!-- Account Information -->
		<Card class="lg:col-span-2">
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Shield class="h-5 w-5" />
					Account Information
				</CardTitle>
				<CardDescription>Your account details are managed by Zitadel</CardDescription>
			</CardHeader>
			<CardContent class="space-y-6">
				<div class="grid gap-4 sm:grid-cols-2">
					<div class="space-y-2">
						<div class="text-muted-foreground text-sm font-medium">Name</div>
						<p class="text-sm">{user?.name || 'Not provided'}</p>
					</div>
					<div class="space-y-2">
						<div class="text-muted-foreground text-sm font-medium">Email</div>
						<p class="text-sm">{user?.email || 'Not provided'}</p>
					</div>
					<div class="space-y-2">
						<div class="text-muted-foreground text-sm font-medium">User ID</div>
						<p class="font-mono text-sm text-xs">{user?.id || 'Not available'}</p>
					</div>
					<div class="space-y-2">
						<div class="text-muted-foreground text-sm font-medium">Email Status</div>
						<div>
							{#if user?.email_verified}
								<Badge variant="default">Verified</Badge>
							{:else}
								<Badge variant="secondary">Not Verified</Badge>
							{/if}
						</div>
					</div>
				</div>

				<Separator />

				<div class="space-y-4">
					<h3 class="text-lg font-medium">Profile Management</h3>
					<p class="text-muted-foreground text-sm">
						To update your profile information, please visit your Zitadel account settings.
					</p>
					<Button
						href="https://zitadel.local.acjzz.xyz"
						target="_blank"
						variant="outline"
						class="gap-2"
					>
						<ExternalLink class="h-4 w-4" />
						Open Zitadel Console
					</Button>
				</div>
			</CardContent>
		</Card>

		<!-- Quick Actions -->
		<Card>
			<CardHeader>
				<CardTitle>Quick Actions</CardTitle>
				<CardDescription>Common account actions</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<Button href="/profile" class="w-full" variant="outline">View Profile</Button>
				<Button href="/dashboard" class="w-full" variant="outline">Dashboard</Button>
				<Button href="/protected" class="w-full" variant="outline">Protected Area</Button>

				<Separator />

				<div class="space-y-2">
					<p class="text-sm font-medium">Sign Out</p>
					<p class="text-muted-foreground mb-2 text-xs">End your current session</p>
					<AuthButton variant="outline" size="sm" showIcon={false} />
				</div>
			</CardContent>
		</Card>
	</div>

	<!-- Application Preferences -->
	<Card>
		<CardHeader>
			<CardTitle class="flex items-center gap-2">
				<Bell class="h-5 w-5" />
				Application Preferences
			</CardTitle>
			<CardDescription>Customize your application experience (demo settings)</CardDescription>
		</CardHeader>
		<CardContent class="space-y-6">
			<div class="grid gap-6 sm:grid-cols-2">
				<div class="flex items-center justify-between">
					<div class="space-y-1">
						<div class="text-sm font-medium">Email Notifications</div>
						<p class="text-muted-foreground text-xs">Receive email updates about your account</p>
					</div>
					<Switch />
				</div>

				<div class="flex items-center justify-between">
					<div class="space-y-1">
						<div class="text-sm font-medium">Push Notifications</div>
						<p class="text-muted-foreground text-xs">Receive push notifications in your browser</p>
					</div>
					<Switch />
				</div>

				<div class="flex items-center justify-between">
					<div class="space-y-1">
						<div class="text-sm font-medium">Dark Mode</div>
						<p class="text-muted-foreground text-xs">Use dark theme for the interface</p>
					</div>
					<Switch />
				</div>

				<div class="flex items-center justify-between">
					<div class="space-y-1">
						<div class="text-sm font-medium">Analytics</div>
						<p class="text-muted-foreground text-xs">Help improve the app with usage analytics</p>
					</div>
					<Switch />
				</div>
			</div>

			<Separator />

			<div class="flex justify-end">
				<Button variant="outline" size="sm">Save Preferences</Button>
			</div>
		</CardContent>
	</Card>

	<!-- Security Information -->
	<Card>
		<CardHeader>
			<CardTitle class="flex items-center gap-2">
				<Eye class="h-5 w-5" />
				Security & Privacy
			</CardTitle>
			<CardDescription>Information about your account security</CardDescription>
		</CardHeader>
		<CardContent class="space-y-4">
			<div class="grid gap-4 sm:grid-cols-2">
				<div class="space-y-2">
					<div class="text-muted-foreground text-sm font-medium">Authentication Provider</div>
					<p class="text-sm">Zitadel OIDC</p>
				</div>
				<div class="space-y-2">
					<div class="text-muted-foreground text-sm font-medium">Session Type</div>
					<p class="text-sm">JWT Token</p>
				</div>
				<div class="space-y-2">
					<div class="text-muted-foreground text-sm font-medium">Last Sign In</div>
					<p class="text-sm">Current session</p>
				</div>
				<div class="space-y-2">
					<div class="text-muted-foreground text-sm font-medium">Security Level</div>
					<Badge variant="default">High</Badge>
				</div>
			</div>

			<div class="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
				<p class="text-sm text-blue-800 dark:text-blue-200">
					Your account is secured with enterprise-grade authentication. All security settings are
					managed through your Zitadel account.
				</p>
			</div>
		</CardContent>
	</Card>
</div>
