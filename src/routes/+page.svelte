<script lang="ts">
	import { page } from '$app/stores';
	import FormExample from '$lib/components/examples/FormExample.svelte';
	import TaskManager from '$lib/components/examples/TaskManager.svelte';
	import UserCard from '$lib/components/examples/UserCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import AuthButton from '$lib/components/auth/AuthButton.svelte';
	import AuthStatus from '$lib/components/auth/AuthStatus.svelte';
	import { Shield, Lock, Users, Zap, CheckCircle } from 'lucide-svelte';

	$: session = $page.data.session;
	$: user = session?.user;

	// Example data for demonstration
	const users = [
		{
			id: 1,
			name: '<PERSON>',
			email: '<EMAIL>',
			avatar:
				'https://api.dicebear.com/7.x/avataaars/svg?seed=Alice&backgroundColor=b6e3f4&clothesColor=262e33&eyebrowType=default&eyeType=happy&facialHairColor=auburn&facialHairType=blank&hairColor=auburn&hatColor=black&mouthType=smile&skinColor=light&topType=longHairStraight',
			role: 'Frontend Developer',
			isOnline: true
		},
		{
			id: 2,
			name: 'Bob Smith',
			email: '<EMAIL>',
			avatar:
				'https://api.dicebear.com/7.x/avataaars/svg?seed=Bob&backgroundColor=c0aede&clothesColor=3c4f5c&eyebrowType=default&eyeType=default&facialHairColor=brown&facialHairType=moustacheFancy&hairColor=brown&hatColor=black&mouthType=default&skinColor=light&topType=shortHairShortFlat',
			role: 'Backend Developer',
			isOnline: false
		},
		{
			id: 3,
			name: 'Carol Davis',
			email: '<EMAIL>',
			avatar:
				'https://api.dicebear.com/7.x/avataaars/svg?seed=Carol&backgroundColor=ffd93d&clothesColor=ff488e&eyebrowType=default&eyeType=happy&facialHairColor=blonde&facialHairType=blank&hairColor=blonde&hatColor=black&mouthType=smile&skinColor=light&topType=longHairCurly',
			role: 'UI/UX Designer',
			isOnline: true
		}
	];

	const features = [
		{
			icon: Shield,
			title: 'Secure Authentication',
			description: 'Enterprise-grade authentication powered by Zitadel OIDC'
		},
		{
			icon: Lock,
			title: 'Route Protection',
			description: 'Automatic route guards and session management'
		},
		{
			icon: Users,
			title: 'User Management',
			description: 'Complete user profile and session handling'
		},
		{
			icon: Zap,
			title: 'Fast & Reliable',
			description: 'Built with SvelteKit 5 and Auth.js for optimal performance'
		}
	];
</script>

<div class="container mx-auto space-y-12 p-8">
	<header class="space-y-6 text-center">
		<div class="space-y-4">
			<h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100">
				SvelteKit 5 + Zitadel Authentication
			</h1>
			<p class="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-400">
				A complete authentication solution with SvelteKit 5, Auth.js, and Zitadel OIDC integration.
			</p>
		</div>

		<!-- Authentication Status -->
		<div class="flex justify-center">
			<AuthStatus showDetails={true} />
		</div>

		<!-- Action Buttons -->
		<div class="flex justify-center gap-4">
			{#if user}
				<Button href="/dashboard" variant="default">Go to Dashboard</Button>
				<Button href="/protected" variant="outline">View Protected Area</Button>
			{:else}
				<AuthButton variant="default" size="default" />
				<Button href="/docs/ZITADEL_SETUP.md" variant="outline">Setup Guide</Button>
			{/if}
		</div>

		<!-- Welcome Message for Authenticated Users -->
		{#if user}
			<div class="mx-auto max-w-md">
				<Card>
					<CardHeader>
						<CardTitle class="flex items-center gap-2 text-green-700 dark:text-green-400">
							<CheckCircle class="h-5 w-5" />
							Welcome back, {user.name || 'User'}!
						</CardTitle>
						<CardDescription>You are successfully authenticated with Zitadel</CardDescription>
					</CardHeader>
				</Card>
			</div>
		{/if}
	</header>

	<!-- Authentication Features -->
	<section class="space-y-6">
		<h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-gray-100">
			Authentication Features
		</h2>

		<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
			{#each features as feature}
				{@const Icon = feature.icon}
				<Card>
					<CardHeader class="text-center">
						<div
							class="bg-primary/10 mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full"
						>
							<Icon class="text-primary h-6 w-6" />
						</div>
						<CardTitle class="text-lg">{feature.title}</CardTitle>
					</CardHeader>
					<CardContent>
						<CardDescription class="text-center">
							{feature.description}
						</CardDescription>
					</CardContent>
				</Card>
			{/each}
		</div>
	</section>

	<!-- Quick Links -->
	<section class="space-y-6">
		<h2 class="text-center text-2xl font-semibold text-gray-900 dark:text-gray-100">Quick Links</h2>

		<div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
			<a
				href="/dashboard"
				class="hover:bg-muted flex items-center gap-3 rounded-lg border p-4 transition-colors"
				class:opacity-50={!user}
				class:pointer-events-none={!user}
			>
				<Shield class="h-5 w-5" />
				<div>
					<div class="font-medium">Dashboard</div>
					<div class="text-muted-foreground text-sm">User dashboard</div>
				</div>
				{#if !user}
					<Badge variant="secondary" class="ml-auto">Auth Required</Badge>
				{/if}
			</a>

			<a
				href="/profile"
				class="hover:bg-muted flex items-center gap-3 rounded-lg border p-4 transition-colors"
				class:opacity-50={!user}
				class:pointer-events-none={!user}
			>
				<Users class="h-5 w-5" />
				<div>
					<div class="font-medium">Profile</div>
					<div class="text-muted-foreground text-sm">User profile</div>
				</div>
				{#if !user}
					<Badge variant="secondary" class="ml-auto">Auth Required</Badge>
				{/if}
			</a>

			<a
				href="/protected"
				class="hover:bg-muted flex items-center gap-3 rounded-lg border p-4 transition-colors"
				class:opacity-50={!user}
				class:pointer-events-none={!user}
			>
				<Lock class="h-5 w-5" />
				<div>
					<div class="font-medium">Protected Area</div>
					<div class="text-muted-foreground text-sm">Secure content</div>
				</div>
				{#if !user}
					<Badge variant="secondary" class="ml-auto">Auth Required</Badge>
				{/if}
			</a>

			<a
				href="/auth/signin"
				class="hover:bg-muted flex items-center gap-3 rounded-lg border p-4 transition-colors"
				class:opacity-50={user}
				class:pointer-events-none={user}
			>
				<Zap class="h-5 w-5" />
				<div>
					<div class="font-medium">Sign In</div>
					<div class="text-muted-foreground text-sm">Authentication</div>
				</div>
				{#if user}
					<Badge variant="secondary" class="ml-auto">Already Signed In</Badge>
				{/if}
			</a>
		</div>
	</section>

	<!-- Example Components -->
	<section class="space-y-6">
		<h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Example Components</h2>

		<!-- User Cards Example -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">User Cards</h3>
			<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
				{#each users as exampleUser (exampleUser.id)}
					<UserCard user={exampleUser} />
				{/each}
			</div>
		</div>

		<!-- Task Manager Example -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">Task Manager</h3>
			<TaskManager />
		</div>

		<!-- Form Example -->
		<div class="space-y-4">
			<h3 class="text-xl font-medium text-gray-800 dark:text-gray-200">Form Example</h3>
			<FormExample />
		</div>
	</section>
</div>
