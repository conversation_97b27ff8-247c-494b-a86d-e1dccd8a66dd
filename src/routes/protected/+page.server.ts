import { requireAuth } from '$lib/auth/guards';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
	// Require authentication to access this page
	const session = await requireAuth(event);

	// Example: You could also check for specific roles here
	// await requireRole(event, ['admin', 'user']);

	return {
		user: session.user,
		message: 'This is a protected area that requires authentication!'
	};
};
