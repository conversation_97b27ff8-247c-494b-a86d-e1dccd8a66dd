<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import AuthStatus from '$lib/components/auth/AuthStatus.svelte';
	import { Shield, Lock, CheckCircle, ArrowLeft } from 'lucide-svelte';
	import { session, isAuthenticated } from '$lib/stores/auth';

	$: user = $session?.user;
	const message = 'This is a protected area that requires authentication!';

	// Client-side authentication check
	onMount(() => {
		if (!$isAuthenticated) {
			goto('/auth/signin');
		}
	});
</script>

<svelte:head>
	<title>Protected Area - SvelteKit Auth</title>
</svelte:head>

<div class="container mx-auto space-y-8 p-8">
	<header class="space-y-4">
		<div class="flex items-center gap-4">
			<Button href="/dashboard" variant="outline" size="sm">
				<ArrowLeft class="mr-2 h-4 w-4" />
				Back to Dashboard
			</Button>
		</div>
		<div class="flex items-center gap-3">
			<div
				class="flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20"
			>
				<Shield class="h-6 w-6 text-green-600 dark:text-green-400" />
			</div>
			<div>
				<h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">Protected Area</h1>
				<p class="text-gray-600 dark:text-gray-400">This page requires authentication to access</p>
			</div>
		</div>
	</header>

	<div class="grid gap-6 lg:grid-cols-2">
		<!-- Success Message -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2 text-green-700 dark:text-green-400">
					<CheckCircle class="h-5 w-5" />
					Access Granted
				</CardTitle>
				<CardDescription>You have successfully accessed this protected resource</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="rounded-lg bg-green-50 p-4 dark:bg-green-900/20">
					<p class="text-sm text-green-800 dark:text-green-200">
						{message}
					</p>
				</div>

				<div class="space-y-2">
					<div class="text-sm font-medium">Authentication Status</div>
					<AuthStatus showDetails={true} />
				</div>

				<div class="space-y-2">
					<div class="text-sm font-medium">Authenticated User</div>
					<p class="text-sm">{user?.name || user?.email || 'Unknown User'}</p>
				</div>
			</CardContent>
		</Card>

		<!-- Security Information -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Lock class="h-5 w-5" />
					Security Features
				</CardTitle>
				<CardDescription>How this page is protected</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="space-y-3">
					<div class="flex items-center gap-2">
						<Badge variant="default" class="gap-1">
							<CheckCircle class="h-3 w-3" />
							Authentication Required
						</Badge>
					</div>
					<div class="flex items-center gap-2">
						<Badge variant="default" class="gap-1">
							<CheckCircle class="h-3 w-3" />
							Session Validation
						</Badge>
					</div>
					<div class="flex items-center gap-2">
						<Badge variant="default" class="gap-1">
							<CheckCircle class="h-3 w-3" />
							OIDC Token Verification
						</Badge>
					</div>
					<div class="flex items-center gap-2">
						<Badge variant="default" class="gap-1">
							<CheckCircle class="h-3 w-3" />
							Automatic Redirects
						</Badge>
					</div>
				</div>

				<div class="border-t pt-4">
					<p class="text-muted-foreground text-xs">
						This page uses server-side authentication guards to ensure only authenticated users can
						access it. Unauthenticated users are automatically redirected to the sign-in page.
					</p>
				</div>
			</CardContent>
		</Card>
	</div>

	<!-- Example Protected Content -->
	<Card>
		<CardHeader>
			<CardTitle>Protected Content Example</CardTitle>
			<CardDescription>This content is only visible to authenticated users</CardDescription>
		</CardHeader>
		<CardContent>
			<div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
				<div class="rounded-lg border p-4">
					<h3 class="font-semibold">User Data</h3>
					<p class="text-muted-foreground text-sm">
						Access to personal user information and preferences
					</p>
				</div>
				<div class="rounded-lg border p-4">
					<h3 class="font-semibold">Secure Operations</h3>
					<p class="text-muted-foreground text-sm">
						Perform actions that require user authentication
					</p>
				</div>
				<div class="rounded-lg border p-4">
					<h3 class="font-semibold">Private Resources</h3>
					<p class="text-muted-foreground text-sm">
						Access to resources specific to the authenticated user
					</p>
				</div>
			</div>
		</CardContent>
	</Card>
</div>
