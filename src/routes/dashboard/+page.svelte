<script lang="ts">
	import { page } from '$app/stores';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar/index.js';
	import AuthStatus from '$lib/components/auth/AuthStatus.svelte';
	import { User, Mail, Calendar, Shield } from 'lucide-svelte';

	$: data = $page.data;
	$: user = data.user;

	const getInitials = (name: string | null | undefined) => {
		if (!name) return 'U';
		return name
			.split(' ')
			.map((n) => n[0])
			.join('')
			.toUpperCase()
			.slice(0, 2);
	};

	const formatDate = (dateString: string | null | undefined) => {
		if (!dateString) return 'Unknown';
		return new Date(dateString).toLocaleDateString();
	};
</script>

<svelte:head>
	<title>Dashboard - SvelteKit Auth</title>
</svelte:head>

<div class="container mx-auto space-y-8 p-8">
	<header class="space-y-4">
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
					Welcome back, {user?.name || 'User'}!
				</h1>
				<p class="text-gray-600 dark:text-gray-400">Here's your personal dashboard</p>
			</div>
			<AuthStatus showDetails={true} />
		</div>
	</header>

	<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
		<!-- User Profile Card -->
		<Card class="col-span-full md:col-span-2">
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<User class="h-5 w-5" />
					Profile Information
				</CardTitle>
				<CardDescription>Your account details from Zitadel</CardDescription>
			</CardHeader>
			<CardContent class="space-y-6">
				<div class="flex items-center gap-4">
					<Avatar class="h-16 w-16">
						<AvatarImage src={user?.image} alt={user?.name || 'User'} />
						<AvatarFallback class="text-lg">{getInitials(user?.name)}</AvatarFallback>
					</Avatar>
					<div class="space-y-1">
						<h3 class="text-xl font-semibold">{user?.name || 'No name provided'}</h3>
						<p class="text-muted-foreground flex items-center gap-1 text-sm">
							<Mail class="h-4 w-4" />
							{user?.email || 'No email provided'}
						</p>
					</div>
				</div>

				<div class="grid gap-4 sm:grid-cols-2">
					<div class="space-y-2">
						<div class="text-sm font-medium">User ID</div>
						<p class="text-muted-foreground font-mono text-sm">
							{user?.id || 'Not available'}
						</p>
					</div>
					<div class="space-y-2">
						<div class="text-sm font-medium">Email Verified</div>
						<div>
							{#if user?.email_verified}
								<Badge variant="default">Verified</Badge>
							{:else}
								<Badge variant="secondary">Not Verified</Badge>
							{/if}
						</div>
					</div>
				</div>
			</CardContent>
		</Card>

		<!-- Session Info Card -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Shield class="h-5 w-5" />
					Session Info
				</CardTitle>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="space-y-2">
					<div class="text-sm font-medium">Status</div>
					<AuthStatus />
				</div>
				<div class="space-y-2">
					<div class="text-sm font-medium">Provider</div>
					<Badge variant="outline">Zitadel</Badge>
				</div>
			</CardContent>
		</Card>
	</div>

	<!-- Quick Actions -->
	<Card>
		<CardHeader>
			<CardTitle>Quick Actions</CardTitle>
			<CardDescription>Common tasks you can perform</CardDescription>
		</CardHeader>
		<CardContent>
			<div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
				<a
					href="/profile"
					class="hover:bg-muted flex items-center gap-2 rounded-lg border p-4 transition-colors"
				>
					<User class="h-5 w-5" />
					<span>Edit Profile</span>
				</a>
				<a
					href="/settings"
					class="hover:bg-muted flex items-center gap-2 rounded-lg border p-4 transition-colors"
				>
					<Shield class="h-5 w-5" />
					<span>Security Settings</span>
				</a>
				<a
					href="/protected"
					class="hover:bg-muted flex items-center gap-2 rounded-lg border p-4 transition-colors"
				>
					<Shield class="h-5 w-5" />
					<span>Protected Area</span>
				</a>
				<a
					href="/"
					class="hover:bg-muted flex items-center gap-2 rounded-lg border p-4 transition-colors"
				>
					<Calendar class="h-5 w-5" />
					<span>Back to Home</span>
				</a>
			</div>
		</CardContent>
	</Card>
</div>
