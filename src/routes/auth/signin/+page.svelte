<script lang="ts">
	import { signIn } from '@auth/sveltekit/client';
	import { Button } from '$lib/components/ui/button/index.js';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card/index.js';
	import { page } from '$app/stores';

	// Get any error from URL params
	$: error = $page.url.searchParams.get('error');
	$: callbackUrl = $page.url.searchParams.get('callbackUrl') || '/';

	const handleSignIn = () => {
		signIn('zitadel', { callbackUrl });
	};
</script>

<svelte:head>
	<title>Sign In - SvelteKit Auth</title>
</svelte:head>

<div class="container mx-auto flex min-h-screen items-center justify-center p-4">
	<Card class="w-full max-w-md">
		<CardHeader class="text-center">
			<CardTitle class="text-2xl font-bold">Welcome Back</CardTitle>
			<CardDescription>Sign in to your account to continue</CardDescription>
		</CardHeader>
		<CardContent class="space-y-4">
			{#if error}
				<div
					class="rounded-md bg-red-50 p-4 text-sm text-red-800 dark:bg-red-900/20 dark:text-red-400"
				>
					{#if error === 'OAuthSignin'}
						There was an error signing in with the OAuth provider.
					{:else if error === 'OAuthCallback'}
						There was an error in the OAuth callback.
					{:else if error === 'OAuthCreateAccount'}
						Could not create OAuth account.
					{:else if error === 'EmailCreateAccount'}
						Could not create email account.
					{:else if error === 'Callback'}
						There was an error in the callback.
					{:else if error === 'OAuthAccountNotLinked'}
						The OAuth account is not linked to any existing account.
					{:else if error === 'EmailSignin'}
						Check your email for a sign in link.
					{:else if error === 'CredentialsSignin'}
						Invalid credentials.
					{:else if error === 'SessionRequired'}
						You must be signed in to access this page.
					{:else}
						An error occurred during authentication: {error}
					{/if}
				</div>
			{/if}

			<!-- svelte-ignore a11y-click-events-have-key-events -->
			<!-- svelte-ignore a11y-no-static-element-interactions -->
			<div onclick={handleSignIn} class="w-full">
				<Button class="w-full" size="lg">Sign in with Zitadel</Button>
			</div>

			<div class="text-center text-sm text-gray-600 dark:text-gray-400">
				By signing in, you agree to our terms of service and privacy policy.
			</div>
		</CardContent>
	</Card>
</div>
