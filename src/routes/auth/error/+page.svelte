<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card/index.js';
	import { page } from '$app/stores';
	import { AlertCircle } from 'lucide-svelte';

	$: error = $page.url.searchParams.get('error');

	const getErrorMessage = (error: string | null) => {
		switch (error) {
			case 'Configuration':
				return 'There is a problem with the server configuration.';
			case 'AccessDenied':
				return 'Access was denied. You may not have permission to sign in.';
			case 'Verification':
				return 'The verification token has expired or has already been used.';
			case 'Default':
			default:
				return 'An unexpected error occurred during authentication.';
		}
	};
</script>

<svelte:head>
	<title>Authentication Error - SvelteKit Auth</title>
</svelte:head>

<div class="container mx-auto flex min-h-screen items-center justify-center p-4">
	<Card class="w-full max-w-md">
		<CardHeader class="text-center">
			<div
				class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20"
			>
				<AlertCircle class="h-6 w-6 text-red-600 dark:text-red-400" />
			</div>
			<CardTitle class="text-2xl font-bold text-red-900 dark:text-red-100">
				Authentication Error
			</CardTitle>
			<CardDescription>
				{getErrorMessage(error)}
			</CardDescription>
		</CardHeader>
		<CardContent class="space-y-4">
			{#if error}
				<div
					class="rounded-md bg-red-50 p-4 text-sm text-red-800 dark:bg-red-900/20 dark:text-red-400"
				>
					<strong>Error Code:</strong>
					{error}
				</div>
			{/if}

			<div class="space-y-2">
				<Button href="/auth/signin" class="w-full">Try Again</Button>
				<Button href="/" variant="outline" class="w-full">Go Home</Button>
			</div>

			<div class="text-center text-sm text-gray-600 dark:text-gray-400">
				If this problem persists, please contact support.
			</div>
		</CardContent>
	</Card>
</div>
