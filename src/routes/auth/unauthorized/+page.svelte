<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card/index.js';
	import { ShieldX } from 'lucide-svelte';
</script>

<svelte:head>
	<title>Unauthorized - SvelteKit Auth</title>
</svelte:head>

<div class="container mx-auto flex min-h-screen items-center justify-center p-4">
	<Card class="w-full max-w-md">
		<CardHeader class="text-center">
			<div
				class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20"
			>
				<ShieldX class="h-6 w-6 text-orange-600 dark:text-orange-400" />
			</div>
			<CardTitle class="text-2xl font-bold text-orange-900 dark:text-orange-100">
				Access Denied
			</CardTitle>
			<CardDescription>You don't have permission to access this resource.</CardDescription>
		</CardHeader>
		<CardContent class="space-y-4">
			<div
				class="rounded-md bg-orange-50 p-4 text-sm text-orange-800 dark:bg-orange-900/20 dark:text-orange-400"
			>
				This page requires specific permissions that your account doesn't have. Please contact an
				administrator if you believe this is an error.
			</div>

			<div class="space-y-2">
				<Button href="/" class="w-full">Go Home</Button>
				<Button href="/profile" variant="outline" class="w-full">View Profile</Button>
			</div>
		</CardContent>
	</Card>
</div>
