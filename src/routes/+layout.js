// Disable server-side rendering for the entire app (SPA mode)
export const ssr = false;

// Enable client-side routing
export const csr = true;

// Disable prerendering for authentication to work properly
export const prerender = false;

// Client-side load function to handle session
export const load = async ({ url }) => {
	// For SPA mode, we'll handle authentication client-side
	// The session will be managed by the auth stores
	return {
		url: url.pathname
	};
};
