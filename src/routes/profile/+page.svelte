<script lang="ts">
	import { page } from '$app/stores';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar/index.js';
	import { User, Mail, Calendar, ArrowLeft } from 'lucide-svelte';

	$: data = $page.data;
	$: user = data.user;

	const getInitials = (name: string | null | undefined) => {
		if (!name) return 'U';
		return name
			.split(' ')
			.map((n) => n[0])
			.join('')
			.toUpperCase()
			.slice(0, 2);
	};
</script>

<svelte:head>
	<title>Profile - SvelteKit Auth</title>
</svelte:head>

<div class="container mx-auto space-y-8 p-8">
	<header class="space-y-4">
		<div class="flex items-center gap-4">
			<Button href="/dashboard" variant="outline" size="sm">
				<ArrowLeft class="mr-2 h-4 w-4" />
				Back to Dashboard
			</Button>
		</div>
		<div>
			<h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">User Profile</h1>
			<p class="text-gray-600 dark:text-gray-400">Manage your account information</p>
		</div>
	</header>

	<div class="grid gap-6 lg:grid-cols-3">
		<!-- Profile Overview -->
		<Card class="lg:col-span-2">
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<User class="h-5 w-5" />
					Profile Information
				</CardTitle>
				<CardDescription>Your personal information from Zitadel</CardDescription>
			</CardHeader>
			<CardContent class="space-y-6">
				<div class="flex items-center gap-6">
					<Avatar class="h-24 w-24">
						<AvatarImage src={user?.image} alt={user?.name || 'User'} />
						<AvatarFallback class="text-2xl">{getInitials(user?.name)}</AvatarFallback>
					</Avatar>
					<div class="space-y-2">
						<h2 class="text-2xl font-semibold">{user?.name || 'No name provided'}</h2>
						<p class="text-muted-foreground flex items-center gap-2">
							<Mail class="h-4 w-4" />
							{user?.email || 'No email provided'}
						</p>
						{#if user?.email_verified}
							<Badge variant="default">Email Verified</Badge>
						{:else}
							<Badge variant="secondary">Email Not Verified</Badge>
						{/if}
					</div>
				</div>

				<div class="grid gap-6 sm:grid-cols-2">
					<div class="space-y-2">
						<div class="text-muted-foreground text-sm font-medium">Full Name</div>
						<p class="text-sm">{user?.name || 'Not provided'}</p>
					</div>
					<div class="space-y-2">
						<div class="text-muted-foreground text-sm font-medium">Email Address</div>
						<p class="text-sm">{user?.email || 'Not provided'}</p>
					</div>
					<div class="space-y-2">
						<div class="text-muted-foreground text-sm font-medium">User ID</div>
						<p class="font-mono text-sm">{user?.id || 'Not available'}</p>
					</div>
					<div class="space-y-2">
						<div class="text-muted-foreground text-sm font-medium">Provider</div>
						<p class="text-sm">Zitadel</p>
					</div>
				</div>
			</CardContent>
		</Card>

		<!-- Account Actions -->
		<Card>
			<CardHeader>
				<CardTitle>Account Actions</CardTitle>
				<CardDescription>Manage your account settings</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<Button href="/settings" class="w-full" variant="outline">Account Settings</Button>
				<Button href="/dashboard" class="w-full" variant="outline">Dashboard</Button>
				<div class="border-t pt-4">
					<p class="text-muted-foreground mb-2 text-xs">
						To update your profile information, please visit your Zitadel account settings.
					</p>
					<Button
						href="https://zitadel.local.acjzz.xyz"
						target="_blank"
						class="w-full"
						variant="secondary"
						size="sm"
					>
						Open Zitadel Console
					</Button>
				</div>
			</CardContent>
		</Card>
	</div>
</div>
