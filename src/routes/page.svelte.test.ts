import { page } from '@vitest/browser/context';
import { describe, expect, it, vi } from 'vitest';
import { render } from 'vitest-browser-svelte';
import { writable } from 'svelte/store';
import Page from './+page.svelte';

// Mock the $app/stores module
vi.mock('$app/stores', () => ({
	page: writable({
		url: new URL('http://localhost:5173'),
		params: {},
		route: { id: '/' },
		data: {
			session: null
		}
	})
}));

describe('/+page.svelte', () => {
	it('should render h1', async () => {
		render(Page);

		const heading = page.getByRole('heading', { level: 1 });
		await expect.element(heading).toBeInTheDocument();
	});
});
