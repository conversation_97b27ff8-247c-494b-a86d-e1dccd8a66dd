import { redirect } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { logAuthError } from '$lib/utils/auth-errors';

/**
 * Protect a route by requiring authentication
 * Redirects to sign-in page if user is not authenticated
 */
export async function requireAuth(event: RequestEvent, redirectTo?: string) {
	try {
		const session = await event.locals.auth();

		if (!session?.user) {
			logAuthError('SessionRequired', {
				path: event.url.pathname,
				userAgent: event.request.headers.get('user-agent')
			});

			const callbackUrl = encodeURIComponent(event.url.pathname + event.url.search);
			const signInUrl = redirectTo || `/auth/signin?callbackUrl=${callbackUrl}`;
			throw redirect(302, signInUrl);
		}

		return session;
	} catch (error) {
		if (error instanceof Response) {
			// Re-throw redirect responses
			throw error;
		}

		logAuthError('Default', {
			error: error instanceof Error ? error.message : 'Unknown error',
			path: event.url.pathname
		});

		throw redirect(302, '/auth/error?error=Default');
	}
}

/**
 * Protect a route by requiring specific user roles or permissions
 * This is a basic example - extend based on your authorization needs
 */
export async function requireRole(
	event: RequestEvent,
	requiredRoles: string[],
	redirectTo?: string
) {
	const session = await requireAuth(event, redirectTo);

	// Example: Check if user has required role
	// You'll need to implement role checking based on your Zitadel setup
	const userRoles = (session.user as any)?.roles || [];
	const hasRequiredRole = requiredRoles.some((role) => userRoles.includes(role));

	if (!hasRequiredRole) {
		throw redirect(302, '/auth/unauthorized');
	}

	return session;
}

/**
 * Redirect authenticated users away from auth pages
 * Useful for sign-in pages when user is already logged in
 */
export async function redirectIfAuthenticated(event: RequestEvent, redirectTo = '/') {
	const session = await event.locals.auth();

	if (session?.user) {
		throw redirect(302, redirectTo);
	}
}

/**
 * Optional authentication - doesn't redirect but provides session info
 * Useful for pages that work for both authenticated and non-authenticated users
 */
export async function optionalAuth(event: RequestEvent) {
	return await event.locals.auth();
}
