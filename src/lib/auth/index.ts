// Re-export authentication guards
export * from './guards';

// Re-export authentication types
export * from '../types/auth';

// Re-export authentication stores
export * from '../stores/auth';

// Re-export Auth.js client functions
export { signIn, signOut } from '@auth/sveltekit/client';

// Utility functions
export const AUTH_ROUTES = {
	SIGNIN: '/auth/signin',
	SIGNOUT: '/auth/signout',
	ERROR: '/auth/error',
	UNAUTHORIZED: '/auth/unauthorized',
	CALLBACK: '/auth/callback'
} as const;

export const PROTECTED_ROUTES = {
	DASHBOARD: '/dashboard',
	PROFILE: '/profile',
	SETTINGS: '/settings',
	PROTECTED: '/protected'
} as const;
