import type { AuthError } from '$lib/types/auth';

/**
 * Enhanced error handling utilities for authentication
 */

export interface ErrorDetails {
	title: string;
	message: string;
	code: string;
	severity: 'error' | 'warning' | 'info';
	retryable: boolean;
	userAction?: string;
}

/**
 * Get detailed error information for an authentication error
 */
export function getErrorDetails(error: AuthError | string): ErrorDetails {
	switch (error) {
		case 'Configuration':
			return {
				title: 'Configuration Error',
				message:
					'There is a problem with the authentication configuration. This is likely a server-side issue.',
				code: 'AUTH_CONFIG_ERROR',
				severity: 'error',
				retryable: false,
				userAction: 'Please contact support for assistance.'
			};

		case 'AccessDenied':
			return {
				title: 'Access Denied',
				message:
					'Access was denied by the authentication provider. You may not have permission to sign in with this account.',
				code: 'AUTH_ACCESS_DENIED',
				severity: 'warning',
				retryable: true,
				userAction: 'Try signing in with a different account or contact your administrator.'
			};

		case 'Verification':
			return {
				title: 'Verification Failed',
				message: 'The verification token has expired or has already been used.',
				code: 'AUTH_VERIFICATION_FAILED',
				severity: 'warning',
				retryable: true,
				userAction: 'Please request a new verification link.'
			};

		case 'OAuthSignin':
			return {
				title: 'OAuth Sign-in Error',
				message: 'There was an error signing in with the OAuth provider.',
				code: 'OAUTH_SIGNIN_ERROR',
				severity: 'error',
				retryable: true,
				userAction: 'Please try signing in again.'
			};

		case 'OAuthCallback':
			return {
				title: 'OAuth Callback Error',
				message: 'There was an error processing the OAuth callback from the provider.',
				code: 'OAUTH_CALLBACK_ERROR',
				severity: 'error',
				retryable: true,
				userAction: 'Please try signing in again.'
			};

		case 'OAuthCreateAccount':
			return {
				title: 'Account Creation Error',
				message: 'Could not create your account with the OAuth provider.',
				code: 'OAUTH_CREATE_ACCOUNT_ERROR',
				severity: 'error',
				retryable: true,
				userAction: 'Please try again or contact support if the problem persists.'
			};

		case 'EmailCreateAccount':
			return {
				title: 'Email Account Creation Error',
				message: 'Could not create your account with the provided email.',
				code: 'EMAIL_CREATE_ACCOUNT_ERROR',
				severity: 'error',
				retryable: true,
				userAction: 'Please check your email address and try again.'
			};

		case 'Callback':
			return {
				title: 'Authentication Callback Error',
				message: 'There was an error in the authentication callback process.',
				code: 'AUTH_CALLBACK_ERROR',
				severity: 'error',
				retryable: true,
				userAction: 'Please try signing in again.'
			};

		case 'OAuthAccountNotLinked':
			return {
				title: 'Account Not Linked',
				message: 'This OAuth account is not linked to any existing account in our system.',
				code: 'OAUTH_ACCOUNT_NOT_LINKED',
				severity: 'warning',
				retryable: false,
				userAction: 'Please contact support to link your account.'
			};

		case 'EmailSignin':
			return {
				title: 'Email Sign-in',
				message: 'A sign-in link has been sent to your email address.',
				code: 'EMAIL_SIGNIN_SENT',
				severity: 'info',
				retryable: false,
				userAction: 'Please check your email and click the sign-in link.'
			};

		case 'CredentialsSignin':
			return {
				title: 'Invalid Credentials',
				message: 'The credentials you provided are invalid.',
				code: 'INVALID_CREDENTIALS',
				severity: 'warning',
				retryable: true,
				userAction: 'Please check your username and password and try again.'
			};

		case 'SessionRequired':
			return {
				title: 'Authentication Required',
				message: 'You must be signed in to access this page.',
				code: 'SESSION_REQUIRED',
				severity: 'info',
				retryable: false,
				userAction: 'Please sign in to continue.'
			};

		case 'Default':
		default:
			return {
				title: 'Authentication Error',
				message: 'An unexpected error occurred during authentication.',
				code: 'AUTH_UNKNOWN_ERROR',
				severity: 'error',
				retryable: true,
				userAction: 'Please try again. If the problem persists, contact support.'
			};
	}
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: AuthError | string): boolean {
	return getErrorDetails(error).retryable;
}

/**
 * Get user-friendly error message
 */
export function getErrorMessage(error: AuthError | string): string {
	return getErrorDetails(error).message;
}

/**
 * Get error severity level
 */
export function getErrorSeverity(error: AuthError | string): 'error' | 'warning' | 'info' {
	return getErrorDetails(error).severity;
}

/**
 * Log authentication errors with context
 */
export function logAuthError(error: AuthError | string, context?: Record<string, unknown>) {
	const details = getErrorDetails(error);

	console.error('Authentication Error:', {
		...details,
		context,
		timestamp: new Date().toISOString()
	});

	// In production, you might want to send this to an error tracking service
	// like Sentry, LogRocket, etc.
	if (
		typeof window !== 'undefined' &&
		(
			window as unknown as {
				gtag?: (event: string, action: string, parameters: Record<string, unknown>) => void;
			}
		).gtag
	) {
		(
			window as unknown as {
				gtag: (event: string, action: string, parameters: Record<string, unknown>) => void;
			}
		).gtag('event', 'auth_error', {
			error_code: details.code,
			error_message: details.message,
			severity: details.severity
		});
	}
}
