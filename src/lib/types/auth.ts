import type { Session, User } from '@auth/sveltekit';

/**
 * Extended user interface with Zitadel-specific properties
 */
export interface Z<PERSON>del<PERSON><PERSON> extends User {
	id: string;
	name?: string | null;
	email?: string | null;
	image?: string | null;
	email_verified?: boolean;
	preferred_username?: string;
	given_name?: string;
	family_name?: string;
	locale?: string;
	phone?: string;
	phone_verified?: boolean;
	roles?: string[];
	permissions?: string[];
	organizations?: string[];
}

/**
 * Extended session interface with Zitadel user
 */
export interface ZitadelSession extends Session {
	user: ZitadelUser;
	accessToken?: string;
	refreshToken?: string;
	expiresAt?: number;
}

/**
 * Authentication state interface
 */
export interface AuthState {
	session: ZitadelSession | null;
	user: ZitadelUser | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	error: string | null;
}

/**
 * Authentication error types
 */
export type AuthError =
	| 'Configuration'
	| 'AccessDenied'
	| 'Verification'
	| 'OAuthSignin'
	| 'OAuthCallback'
	| 'OAuthCreateAccount'
	| 'EmailCreateAccount'
	| 'Callback'
	| 'OAuthAccountNotLinked'
	| 'EmailSignin'
	| 'CredentialsSignin'
	| 'SessionRequired'
	| 'Default';

/**
 * Sign-in options
 */
export interface SignInOptions {
	callbackUrl?: string;
	redirect?: boolean;
}

/**
 * Sign-out options
 */
export interface SignOutOptions {
	callbackUrl?: string;
	redirect?: boolean;
}

/**
 * Route protection options
 */
export interface RouteProtectionOptions {
	requireAuth?: boolean;
	requiredRoles?: string[];
	requiredPermissions?: string[];
	redirectTo?: string;
	allowUnauthenticated?: boolean;
}

/**
 * User role types (customize based on your Zitadel setup)
 */
export type UserRole = 'admin' | 'user' | 'moderator' | 'viewer' | string; // Allow custom roles

/**
 * User permission types (customize based on your Zitadel setup)
 */
export type UserPermission = 'read' | 'write' | 'delete' | 'admin' | string; // Allow custom permissions

/**
 * Authentication provider configuration
 */
export interface AuthProviderConfig {
	clientId: string;
	clientSecret: string;
	issuer: string;
	scope?: string;
}

/**
 * Authentication callback data
 */
export interface AuthCallbackData {
	user: ZitadelUser;
	account: {
		provider: string;
		type: string;
		providerAccountId: string;
		access_token?: string;
		refresh_token?: string;
		expires_at?: number;
	};
	profile: Record<string, unknown>;
}

/**
 * Authentication event types
 */
export type AuthEvent = 'signin' | 'signout' | 'session' | 'error';

/**
 * Authentication event handler
 */
export interface AuthEventHandler {
	(event: AuthEvent, data?: unknown): void | Promise<void>;
}

/**
 * Type guards for authentication
 */
export const isZitadelUser = (user: unknown): user is ZitadelUser => {
	return (
		user &&
		typeof user === 'object' &&
		user !== null &&
		'id' in user &&
		typeof (user as { id: unknown }).id === 'string'
	);
};

export const isZitadelSession = (session: unknown): session is ZitadelSession => {
	return (
		session &&
		typeof session === 'object' &&
		session !== null &&
		'user' in session &&
		isZitadelUser((session as { user: unknown }).user)
	);
};

export const isAuthError = (error: unknown): error is AuthError => {
	const validErrors: AuthError[] = [
		'Configuration',
		'AccessDenied',
		'Verification',
		'OAuthSignin',
		'OAuthCallback',
		'OAuthCreateAccount',
		'EmailCreateAccount',
		'Callback',
		'OAuthAccountNotLinked',
		'EmailSignin',
		'CredentialsSignin',
		'SessionRequired',
		'Default'
	];
	return typeof error === 'string' && validErrors.includes(error as AuthError);
};
