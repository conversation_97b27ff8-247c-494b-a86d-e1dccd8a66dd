<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { authError, authHelpers } from '$lib/stores/auth';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Alert, AlertDescription } from '$lib/components/ui/alert/index.js';
	import { AlertCircle, RefreshCw, Home } from 'lucide-svelte';
	import type { AuthError } from '$lib/types/auth';

	const { children } = $props();

	const getErrorMessage = (error: AuthError | string): string => {
		switch (error) {
			case 'Configuration':
				return 'There is a problem with the authentication configuration. Please contact support.';
			case 'AccessDenied':
				return 'Access was denied. You may not have permission to sign in with this account.';
			case 'Verification':
				return 'The verification token has expired or has already been used.';
			case 'OAuthSignin':
				return 'There was an error signing in with the OAuth provider. Please try again.';
			case 'OAuthCallback':
				return 'There was an error processing the OAuth callback. Please try signing in again.';
			case 'OAuthCreateAccount':
				return 'Could not create your account with the OAuth provider. Please try again.';
			case 'EmailCreateAccount':
				return 'Could not create your account with email. Please try again.';
			case 'Callback':
				return 'There was an error in the authentication callback. Please try again.';
			case 'OAuthAccountNotLinked':
				return 'This OAuth account is not linked to any existing account. Please contact support.';
			case 'EmailSignin':
				return 'Check your email for a sign-in link.';
			case 'CredentialsSignin':
				return 'Invalid credentials provided. Please check your login information.';
			case 'SessionRequired':
				return 'You must be signed in to access this page.';
			case 'Default':
			default:
				return 'An unexpected error occurred during authentication. Please try again.';
		}
	};

	const getErrorTitle = (error: AuthError | string): string => {
		switch (error) {
			case 'Configuration':
				return 'Configuration Error';
			case 'AccessDenied':
				return 'Access Denied';
			case 'Verification':
				return 'Verification Failed';
			case 'OAuthSignin':
			case 'OAuthCallback':
			case 'OAuthCreateAccount':
				return 'OAuth Error';
			case 'EmailCreateAccount':
			case 'EmailSignin':
				return 'Email Authentication Error';
			case 'Callback':
				return 'Callback Error';
			case 'OAuthAccountNotLinked':
				return 'Account Not Linked';
			case 'CredentialsSignin':
				return 'Invalid Credentials';
			case 'SessionRequired':
				return 'Authentication Required';
			case 'Default':
			default:
				return 'Authentication Error';
		}
	};

	const handleRetry = () => {
		authHelpers.clearError();
		// Optionally reload the page or redirect to sign-in
		window.location.reload();
	};

	const handleGoHome = () => {
		authHelpers.clearError();
		window.location.href = '/';
	};

	// Check for URL error parameters on mount
	onMount(() => {
		const urlError = $page.url.searchParams.get('error');
		if (urlError) {
			authHelpers.setError(urlError);
		}
	});
</script>

{#if $authError}
	<div class="container mx-auto flex min-h-screen items-center justify-center p-4">
		<Card class="w-full max-w-md">
			<CardHeader class="text-center">
				<div
					class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20"
				>
					<AlertCircle class="h-6 w-6 text-red-600 dark:text-red-400" />
				</div>
				<CardTitle class="text-2xl font-bold text-red-900 dark:text-red-100">
					{getErrorTitle($authError)}
				</CardTitle>
				<CardDescription>
					{getErrorMessage($authError)}
				</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<Alert variant="destructive">
					<AlertCircle class="h-4 w-4" />
					<AlertDescription>
						<strong>Error Code:</strong>
						{$authError}
					</AlertDescription>
				</Alert>

				<div class="space-y-2">
					<!-- svelte-ignore a11y_click_events_have_key_events -->
					<!-- svelte-ignore a11y_no_static_element_interactions -->
					<div onclick={handleRetry} class="w-full" role="button" tabindex="0">
						<Button class="w-full gap-2">
							<RefreshCw class="h-4 w-4" />
							Try Again
						</Button>
					</div>
					<!-- svelte-ignore a11y_click_events_have_key_events -->
					<!-- svelte-ignore a11y_no_static_element_interactions -->
					<div onclick={handleGoHome} class="w-full" role="button" tabindex="0">
						<Button variant="outline" class="w-full gap-2">
							<Home class="h-4 w-4" />
							Go Home
						</Button>
					</div>
				</div>

				<div class="text-center text-sm text-gray-600 dark:text-gray-400">
					If this problem persists, please contact support with the error code above.
				</div>
			</CardContent>
		</Card>
	</div>
{:else}
	{@render children()}
{/if}
