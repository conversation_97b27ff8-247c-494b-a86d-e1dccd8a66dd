<script lang="ts">
	import { signIn, signOut } from '@auth/sveltekit/client';
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button/index.js';
	import { LogIn, LogOut } from 'lucide-svelte';

	export let variant: 'default' | 'outline' | 'ghost' = 'default';
	export let size: 'sm' | 'default' | 'lg' = 'default';
	export let showIcon = true;
	export let callbackUrl = '/';

	$: session = $page.data.session;

	const handleSignIn = () => {
		signIn('zitadel', { callbackUrl });
	};

	const handleSignOut = () => {
		signOut({ callbackUrl: '/' });
	};
</script>

{#if session?.user}
	<!-- svelte-ignore a11y-click-events-have-key-events -->
	<!-- svelte-ignore a11y-no-static-element-interactions -->
	<div onclick={handleSignOut} class="inline-block">
		<Button {variant} {size} class="gap-2">
			{#if showIcon}
				<LogOut class="h-4 w-4" />
			{/if}
			Sign Out
		</Button>
	</div>
{:else}
	<!-- svelte-ignore a11y-click-events-have-key-events -->
	<!-- svelte-ignore a11y-no-static-element-interactions -->
	<div onclick={handleSignIn} class="inline-block">
		<Button {variant} {size} class="gap-2">
			{#if showIcon}
				<LogIn class="h-4 w-4" />
			{/if}
			Sign In
		</Button>
	</div>
{/if}
