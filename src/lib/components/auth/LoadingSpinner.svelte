<script lang="ts">
	import { authLoading } from '$lib/stores/auth';
	import { Loader2 } from 'lucide-svelte';

	export let size: 'sm' | 'md' | 'lg' = 'md';
	export let text = 'Authenticating...';
	export let overlay = false;

	const sizeClasses = {
		sm: 'h-4 w-4',
		md: 'h-6 w-6',
		lg: 'h-8 w-8'
	};
</script>

{#if $authLoading}
	<div
		class="flex items-center justify-center gap-2"
		class:fixed={overlay}
		class:inset-0={overlay}
		class:backdrop-blur-sm={overlay}
		class:z-50={overlay}
		style:background-color={overlay ? 'hsl(var(--background) / 0.8)' : undefined}
	>
		<div class="flex items-center gap-2">
			<Loader2 class="{sizeClasses[size]} animate-spin" />
			{#if text}
				<span class="text-muted-foreground text-sm">{text}</span>
			{/if}
		</div>
	</div>
{/if}
