<script lang="ts">
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button/index.js';
	import AuthButton from './AuthButton.svelte';
	import UserProfile from './UserProfile.svelte';
	import { Home, Shield } from 'lucide-svelte';

	$: session = $page.data.session;
</script>

<nav class="bg-background/95 supports-[backdrop-filter]:bg-background/60 border-b backdrop-blur">
	<div class="container mx-auto flex h-14 items-center justify-between px-4">
		<!-- Logo/Brand -->
		<div class="flex items-center gap-2">
			<a href="/" class="flex items-center gap-2 font-semibold">
				<Shield class="h-6 w-6" />
				SvelteKit Auth
			</a>
		</div>

		<!-- Navigation Links -->
		<div class="flex items-center gap-4">
			<Button href="/" variant="ghost" size="sm">
				<Home class="mr-2 h-4 w-4" />
				Home
			</Button>

			{#if session?.user}
				<Button href="/dashboard" variant="ghost" size="sm">Dashboard</Button>
				<Button href="/profile" variant="ghost" size="sm">Profile</Button>
			{/if}
		</div>

		<!-- Auth Section -->
		<div class="flex items-center gap-2">
			{#if session?.user}
				<UserProfile />
			{:else}
				<AuthButton variant="default" size="sm" />
			{/if}
		</div>
	</div>
</nav>
