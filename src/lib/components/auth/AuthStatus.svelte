<script lang="ts">
	import { page } from '$app/stores';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { CheckCircle, XCircle } from 'lucide-svelte';

	export let showDetails = false;

	$: session = $page.data.session;
	$: user = session?.user;
</script>

<div class="flex items-center gap-2">
	{#if session}
		<Badge variant="default" class="gap-1">
			<CheckCircle class="h-3 w-3" />
			Authenticated
		</Badge>
		{#if showDetails && user}
			<span class="text-muted-foreground text-sm">
				as {user.name || user.email || 'User'}
			</span>
		{/if}
	{:else}
		<Badge variant="secondary" class="gap-1">
			<XCircle class="h-3 w-3" />
			Not Authenticated
		</Badge>
	{/if}
</div>
