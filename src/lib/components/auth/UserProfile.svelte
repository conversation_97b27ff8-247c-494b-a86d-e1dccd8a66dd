<script lang="ts">
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar/index.js';
	import { signOut } from '@auth/sveltekit/client';
	import { LogOut } from 'lucide-svelte';

	$: session = $page.data.session;
	$: user = session?.user;

	const handleSignOut = () => {
		signOut({ callbackUrl: '/' });
	};

	const getInitials = (name: string | null | undefined) => {
		if (!name) return 'U';
		return name
			.split(' ')
			.map((n) => n[0])
			.join('')
			.toUpperCase()
			.slice(0, 2);
	};
</script>

{#if user}
	<div class="flex items-center gap-2">
		<Avatar class="h-8 w-8">
			<AvatarImage src={user.image} alt={user.name || 'User'} />
			<AvatarFallback>{getInitials(user.name)}</AvatarFallback>
		</Avatar>
		<div class="flex flex-col">
			<span class="text-sm font-medium">{user.name || 'User'}</span>
			<span class="text-muted-foreground text-xs">{user.email || 'No email'}</span>
		</div>
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<div onclick={handleSignOut} class="ml-2">
			<Button variant="ghost" size="sm" class="gap-1">
				<LogOut class="h-4 w-4" />
				Sign out
			</Button>
		</div>
	</div>
{/if}
