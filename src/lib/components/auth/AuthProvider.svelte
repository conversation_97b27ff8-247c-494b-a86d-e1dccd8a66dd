<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { authHelpers } from '$lib/stores/auth';
	import { browser } from '$app/environment';

	const { children } = $props();

	// Handle authentication state changes and errors
	onMount(() => {
		if (!browser) return;

		// Clear any existing errors on mount
		authHelpers.clearError();
	});

	// Monitor page changes for authentication state using Svelte 5 $effect
	$effect(() => {
		if (browser && $page.url) {
			// Clear loading state when page changes
			authHelpers.setLoading(false);

			// Handle authentication errors from URL params
			const error = $page.url.searchParams.get('error');
			if (error) {
				authHelpers.setError(error);
			} else {
				authHelpers.clearError();
			}
		}
	});
</script>

<!--
	This component provides authentication context to the entire application.
	It handles authentication state management and error handling.
	Place this component at the root of your application.
-->
{@render children()}
