import { writable, derived } from 'svelte/store';
import { page } from '$app/stores';
import type { ZitadelSession, ZitadelUser } from '$lib/types/auth';

/**
 * Auth store that provides reactive access to authentication state
 * This store is derived from the page data and automatically updates
 * when the session changes
 */
export const session = derived(page, ($page) => $page.data.session as ZitadelSession | null);

/**
 * User store that provides reactive access to the current user
 */
export const user = derived(session, ($session) => $session?.user as ZitadelUser | null);

/**
 * Authentication status store
 */
export const isAuthenticated = derived(session, ($session) => !!$session?.user);

/**
 * Loading state for authentication operations
 */
export const authLoading = writable(false);

/**
 * Error state for authentication operations
 */
export const authError = writable<string | null>(null);

/**
 * Helper functions for authentication state management
 */
export const authHelpers = {
	/**
	 * Set loading state
	 */
	setLoading: (loading: boolean) => {
		authLoading.set(loading);
	},

	/**
	 * Set error state
	 */
	setError: (error: string | null) => {
		authError.set(error);
	},

	/**
	 * Clear error state
	 */
	clearError: () => {
		authError.set(null);
	},

	/**
	 * Check if user has a specific role (example implementation)
	 * You'll need to customize this based on your Zitadel setup
	 */
	hasRole: (requiredRole: string, userSession: ZitadelSession | null = null) => {
		// This is a placeholder implementation
		// You'll need to implement role checking based on your Zitadel configuration
		const currentSession = userSession || get(session);
		const userRoles = currentSession?.user?.roles || [];
		return userRoles.includes(requiredRole);
	},

	/**
	 * Check if user has any of the specified roles
	 */
	hasAnyRole: (requiredRoles: string[], userSession: ZitadelSession | null = null) => {
		const currentSession = userSession || get(session);
		const userRoles = currentSession?.user?.roles || [];
		return requiredRoles.some((role) => userRoles.includes(role));
	},

	/**
	 * Get user's display name
	 */
	getDisplayName: (userSession: ZitadelSession | null = null) => {
		const currentSession = userSession || get(session);
		const user = currentSession?.user;
		return user?.name || user?.email || 'User';
	},

	/**
	 * Get user's initials for avatar
	 */
	getInitials: (userSession: ZitadelSession | null = null) => {
		const currentSession = userSession || get(session);
		const user = currentSession?.user;
		const name = user?.name || user?.email || 'User';
		return name
			.split(' ')
			.map((n) => n[0])
			.join('')
			.toUpperCase()
			.slice(0, 2);
	}
};

// Import get function for internal use
import { get } from 'svelte/store';
