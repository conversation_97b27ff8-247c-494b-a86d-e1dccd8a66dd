// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
/// <reference types="@auth/sveltekit" />

declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			auth: import('@auth/sveltekit').Session | null;
		}
		interface PageData {
			session: import('@auth/sveltekit').Session | null;
		}
		// interface PageState {}
		// interface Platform {}
	}
}

export {};
