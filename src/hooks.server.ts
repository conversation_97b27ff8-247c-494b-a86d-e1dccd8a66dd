import { handle as authHandle } from './auth';
import { sequence } from '@sveltejs/kit/hooks';

// You can add additional hooks here if needed
const customHandle: import('@sveltejs/kit').Handle = async ({ event, resolve }) => {
	// Add any custom server-side logic here
	return resolve(event);
};

// Combine Auth.js handle with any custom handles
export const handle = sequence(authHandle, customHandle);
