import { <PERSON>velte<PERSON>itA<PERSON> } from '@auth/sveltekit';
import Zitadel from '@auth/core/providers/zitadel';
import {
	AUTH_SECRET,
	ZITADEL_ISSUER,
	ZITADEL_CLIENT_ID,
	ZITADEL_CLIENT_SECRET
} from '$env/static/private';

export const { handle, signIn, signOut } = SvelteKitAuth({
	providers: [
		Zitadel({
			clientId: ZITADEL_CLIENT_ID,
			clientSecret: ZITADEL_CLIENT_SECRET,
			issuer: ZITADEL_ISSUER,
			// Request additional scopes for user profile information
			authorization: {
				params: {
					scope: 'openid email profile'
				}
			}
		})
	],
	secret: AUTH_SECRET,
	trustHost: true,
	callbacks: {
		async session({ session, token }) {
			// Add custom fields to session if needed
			if (token?.sub) {
				session.user.id = token.sub;
			}
			return session;
		},
		async jwt({ token, account, profile }) {
			// Persist additional user information in JWT
			if (account && profile) {
				token.accessToken = account.access_token;
				token.refreshToken = account.refresh_token;
			}
			return token;
		},
		async signIn({ account, profile }) {
			// Optional: Add custom sign-in logic
			// For example, check if email is verified
			if (account?.provider === 'zitadel') {
				return profile?.email_verified ?? true;
			}
			return true;
		}
	},
	pages: {
		signIn: '/auth/signin',
		error: '/auth/error'
	}
});
